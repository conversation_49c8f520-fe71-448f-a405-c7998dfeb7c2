{"name": "ai_photo", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@radix-ui/react-select": "^2.2.6", "@tailwindcss/vite": "^4.1.12", "@tauri-apps/api": "^2", "@tauri-apps/plugin-dialog": "^2", "@tauri-apps/plugin-opener": "^2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.539.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.8.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@shadcn/ui": "^0.0.4", "@tailwindcss/postcss": "^4.1.12", "@tauri-apps/cli": "^2", "@types/node": "^24.3.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.12", "tailwindcss-animate": "^1.0.7", "typescript": "~5.8.3", "vite": "^7.0.4"}}
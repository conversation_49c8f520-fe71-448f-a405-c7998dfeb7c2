import { useState, useEffect, useCallback } from 'react';

export interface TimerState {
  startTime: number | null;
  elapsedTime: number;
  isRunning: boolean;
}

export const useTimer = () => {
  const [startTime, setStartTime] = useState<number | null>(null);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [isRunning, setIsRunning] = useState(false);

  // 计时器效果
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;
    
    if (isRunning && startTime) {
      interval = setInterval(() => {
        setElapsedTime(Date.now() - startTime);
      }, 100); // 每100ms更新一次
    }
    
    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [isRunning, startTime]);

  // 开始计时
  const start = useCallback(() => {
    const now = Date.now();
    setStartTime(now);
    setElapsedTime(0);
    setIsRunning(true);
  }, []);

  // 停止计时
  const stop = useCallback(() => {
    setIsRunning(false);
  }, []);

  // 重置计时器
  const reset = useCallback(() => {
    setStartTime(null);
    setElapsedTime(0);
    setIsRunning(false);
  }, []);

  // 格式化时间显示
  const formatTime = useCallback((milliseconds: number) => {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    const remainingMilliseconds = Math.floor((milliseconds % 1000) / 100);
    
    if (minutes > 0) {
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}.${remainingMilliseconds}`;
    } else {
      return `${remainingSeconds}.${remainingMilliseconds}`;
    }
  }, []);

  return {
    startTime,
    elapsedTime,
    isRunning,
    start,
    stop,
    reset,
    formatTime,
  };
};

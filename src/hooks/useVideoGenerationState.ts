import { useState, useEffect, useCallback } from 'react';

export interface VideoGenerationState {
  prompt: string;
  imageUrl: string;
  taskId: string;
  videoUrl: string;
  isProcessing: boolean;
  isPolling: boolean;
  error: string;
  startTime: number | null;
  elapsedTime: number;
  isTimerRunning: boolean;
}

const STORAGE_KEY = 'video_generation_state';

const defaultState: VideoGenerationState = {
  prompt: '',
  imageUrl: '',
  taskId: '',
  videoUrl: '',
  isProcessing: false,
  isPolling: false,
  error: '',
  startTime: null,
  elapsedTime: 0,
  isTimerRunning: false,
};

export const useVideoGenerationState = () => {
  const [state, setState] = useState<VideoGenerationState>(() => {
    // 从localStorage加载初始状态
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      if (saved) {
        const parsedState = JSON.parse(saved);
        // 页面刷新后，重置运行状态
        return {
          ...parsedState,
          isProcessing: false,
          isPolling: false,
          isTimerRunning: false,
        };
      }
    } catch (error) {
      console.error('Failed to load video generation state from localStorage:', error);
    }
    return defaultState;
  });

  // 保存状态到localStorage
  const saveState = useCallback((newState: VideoGenerationState) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(newState));
    } catch (error) {
      console.error('Failed to save video generation state to localStorage:', error);
    }
  }, []);

  // 更新状态的函数
  const updateState = useCallback((updates: Partial<VideoGenerationState>) => {
    setState(prevState => {
      const newState = { ...prevState, ...updates };
      saveState(newState);
      return newState;
    });
  }, [saveState]);

  // 重置状态
  const resetState = useCallback(() => {
    setState(defaultState);
    try {
      localStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.error('Failed to remove video generation state from localStorage:', error);
    }
  }, []);

  // 清除任务相关状态（保留输入内容）
  const clearTask = useCallback(() => {
    updateState({
      taskId: '',
      videoUrl: '',
      isProcessing: false,
      isPolling: false,
      error: '',
      startTime: null,
      elapsedTime: 0,
      isTimerRunning: false,
    });
  }, [updateState]);

  return {
    state,
    updateState,
    resetState,
    clearTask,
  };
};

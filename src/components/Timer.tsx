import React from 'react';
import { Clock } from 'lucide-react';

interface TimerProps {
  elapsedTime: number;
  isRunning: boolean;
  formatTime: (milliseconds: number) => string;
  className?: string;
}

export const Timer: React.FC<TimerProps> = ({ 
  elapsedTime, 
  isRunning, 
  formatTime, 
  className = "" 
}) => {
  if (elapsedTime === 0 && !isRunning) {
    return null;
  }

  return (
    <div className={`bg-green-50 border border-green-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-center gap-2">
        <Clock className="h-4 w-4 text-green-600" />
        <span className="text-green-700 font-medium">
          {isRunning ? '处理时间' : '总用时'}
        </span>
      </div>
      <div className="mt-2">
        <span className="text-2xl font-mono font-bold text-green-800">
          {formatTime(elapsedTime)}s
        </span>
        {isRunning && (
          <span className="text-sm text-green-600 ml-2">进行中...</span>
        )}
      </div>
    </div>
  );
};

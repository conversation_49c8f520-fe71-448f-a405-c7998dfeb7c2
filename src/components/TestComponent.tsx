import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export function TestComponent() {
  return (
    <div className="p-4 md:p-8 space-y-4 md:space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-2xl md:text-4xl font-bold text-primary">Tailwind CSS v4 + shadcn/ui</h1>
        <p className="text-base md:text-lg text-muted-foreground">
          配置成功！🎉
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>按钮组件测试</CardTitle>
          <CardDescription>
            测试不同变体的shadcn/ui按钮组件
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2 md:gap-4">
            <Button>默认按钮</Button>
            <Button variant="secondary">次要按钮</Button>
            <Button variant="outline">轮廓按钮</Button>
            <Button variant="destructive">危险按钮</Button>
            <Button variant="ghost">幽灵按钮</Button>
            <Button variant="link">链接按钮</Button>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Tailwind CSS v4 特性</CardTitle>
            <CardDescription>新版本的主要改进</CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                更快的构建速度
              </li>
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                改进的CSS变量支持
              </li>
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                更好的开发体验
              </li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>shadcn/ui 组件</CardTitle>
            <CardDescription>现代化的React组件库</CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                完全可定制
              </li>
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                TypeScript支持
              </li>
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                无障碍访问
              </li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

import React, { useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Play, Download, Image, Clock, RotateCcw } from 'lucide-react';
import { useVideoGenerationState } from '@/hooks/useVideoGenerationState';

const VideoGeneration: React.FC = () => {
  // 使用设置中的豆包API密钥
  const douBaoApiKey = "2e3e1b1f-b6fb-46c7-a519-343d3146b88f";

  // 使用持久化状态Hook
  const { state, updateState, clearTask } = useVideoGenerationState();

  // 解构状态以便使用
  const {
    prompt,
    imageUrl,
    taskId,
    videoUrl,
    isProcessing,
    isPolling,
    error,
    startTime,
    elapsedTime,
    isTimerRunning,
  } = state;

  // 计时器效果
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;

    if (isTimerRunning && startTime) {
      interval = setInterval(() => {
        updateState({ elapsedTime: Date.now() - startTime });
      }, 100); // 每100ms更新一次
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [isTimerRunning, startTime, updateState]);

  // 格式化时间显示
  const formatTime = (milliseconds: number) => {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    const remainingMilliseconds = Math.floor((milliseconds % 1000) / 100);

    if (minutes > 0) {
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}.${remainingMilliseconds}`;
    } else {
      return `${remainingSeconds}.${remainingMilliseconds}`;
    }
  };

  // 轮询任务结果
  const pollTaskResult = async (taskId: string) => {
    updateState({ isPolling: true });
    const maxAttempts = 60; // 最多轮询60次，视频生成通常需要更长时间
    let attempts = 0;

    const poll = async () => {
      try {
        const result: any = await invoke("get_doubao_video_result", {
          apiKey: douBaoApiKey,
          taskId: taskId,
        });

        console.log("视频生成任务查询结果:", result);

        if (result.status === "succeeded") {
          // 任务成功完成
          if (result.content && result.content.video_url) {
            updateState({
              videoUrl: result.content.video_url,
              isPolling: false,
              isProcessing: false,
              isTimerRunning: false,
            });
            return;
          }
        } else if (result.status === "failed") {
          // 任务失败
          updateState({
            error: `视频生成失败: ${result.message || "未知错误"}`,
            isPolling: false,
            isProcessing: false,
            isTimerRunning: false,
          });
          return;
        }

        // 任务还在处理中，继续轮询
        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000); // 5秒后再次查询，视频生成需要更长间隔
        } else {
          updateState({
            error: "视频生成超时，请稍后重试",
            isPolling: false,
            isProcessing: false,
            isTimerRunning: false,
          });
        }
      } catch (err) {
        console.error("查询视频生成结果失败:", err);
        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000);
        } else {
          updateState({
            error: `查询视频生成结果失败: ${err}`,
            isPolling: false,
            isProcessing: false,
            isTimerRunning: false,
          });
        }
      }
    };

    poll();
  };

  const handleStartGeneration = async () => {
    // 验证必需的输入
    if (!prompt) {
      updateState({ error: "请提供视频生成提示词" });
      return;
    }

    // 开始计时
    const now = Date.now();
    updateState({
      error: '',
      videoUrl: '',
      taskId: '',
      isProcessing: true,
      startTime: now,
      elapsedTime: 0,
      isTimerRunning: true,
    });

    try {
      // 创建视频生成任务
      const result: any = await invoke("create_doubao_video_task", {
        apiKey: douBaoApiKey,
        prompt: prompt,
        imageUrl: imageUrl || null,
      });

      console.log("创建视频生成任务结果:", result);

      if (result.id) {
        updateState({ taskId: result.id });
        // 开始轮询任务结果
        pollTaskResult(result.id);
      } else {
        updateState({
          error: "创建视频生成任务失败",
          isProcessing: false,
          isTimerRunning: false,
        });
      }
    } catch (err) {
      updateState({
        error: `创建视频生成任务失败: ${err}`,
        isProcessing: false,
        isTimerRunning: false,
      });
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">豆包视频生成</h1>
        <p className="text-gray-600">使用豆包AI生成高质量视频内容</p>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 配置区域 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Play className="h-5 w-5" />
              视频生成配置
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">

            {/* 提示词输入 */}
            <div className="space-y-2">
              <Label htmlFor="prompt">视频生成提示词</Label>
              <Textarea
                id="prompt"
                placeholder="描述您想要生成的视频内容，例如：女孩抱着狐狸，女孩睁开眼，温柔地看向镜头，狐狸友善地抱着，镜头缓缓拉出，女孩的头发被风吹动 --ratio adaptive --dur 5"
                value={prompt}
                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => updateState({ prompt: e.target.value })}
                rows={4}
              />
              <p className="text-xs text-gray-500">
                提示：可以使用 --ratio adaptive --dur 5 等参数控制视频比例和时长
              </p>
            </div>

            {/* 参考图片URL */}
            <div className="space-y-2">
              <Label htmlFor="image-url">参考图片URL（可选）</Label>
              <Input
                id="image-url"
                type="url"
                placeholder="https://example.com/image.jpg"
                value={imageUrl}
                onChange={(e) => updateState({ imageUrl: e.target.value })}
              />
              {imageUrl && (
                <div className="mt-2 bg-gray-50 rounded-md p-2 border">
                  <img
                    src={imageUrl}
                    alt="参考图片预览"
                    className="w-full h-40 object-contain rounded-md"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                </div>
              )}
            </div>

            {/* 生成按钮 */}
            <div className="space-y-2">
              <Button
                onClick={handleStartGeneration}
                disabled={isProcessing || !prompt}
                className="w-full"
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {isPolling ? "生成中..." : "创建任务中..."}
                  </>
                ) : (
                  <>
                    <Play className="mr-2 h-4 w-4" />
                    开始生成视频
                  </>
                )}
              </Button>

              {/* 清除任务按钮 */}
              {(taskId || videoUrl || error) && !isProcessing && (
                <Button
                  onClick={clearTask}
                  variant="outline"
                  size="sm"
                  className="w-full"
                >
                  <RotateCcw className="mr-2 h-4 w-4" />
                  清除任务
                </Button>
              )}
            </div>

            {/* 任务状态和计时器 */}
            {taskId && (
              <div className="space-y-3">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <p className="text-blue-700 font-medium">任务已创建</p>
                  <p className="text-xs text-blue-600 mt-1">任务ID: {taskId}</p>
                  {isPolling && (
                    <p className="text-xs text-blue-600 mt-1">正在生成视频，请耐心等待...</p>
                  )}
                </div>

                {/* 计时器显示 */}
                {(isTimerRunning || elapsedTime > 0) && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-green-600" />
                      <span className="text-green-700 font-medium">
                        {isTimerRunning ? '生成时间' : '总用时'}
                      </span>
                    </div>
                    <div className="mt-2">
                      <span className="text-2xl font-mono font-bold text-green-800">
                        {formatTime(elapsedTime)}s
                      </span>
                      {isTimerRunning && (
                        <span className="text-sm text-green-600 ml-2">进行中...</span>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* 结果区域 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Image className="h-5 w-5" />
              生成结果
            </CardTitle>
          </CardHeader>
          <CardContent>
            {videoUrl ? (
              <div className="space-y-4">
                <div className="bg-gray-50 rounded-lg p-4 border">
                  <video
                    src={videoUrl}
                    controls
                    className="w-full max-h-[400px] rounded-lg"
                    poster=""
                  >
                    您的浏览器不支持视频播放
                  </video>
                </div>
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      const link = document.createElement('a');
                      link.href = videoUrl;
                      link.download = 'generated-video.mp4';
                      link.click();
                    }}
                  >
                    <Download className="mr-2 h-4 w-4" />
                    下载视频
                  </Button>
                </div>
              </div>
            ) : (
              <div className="text-center py-12 text-gray-500">
                <Play className="mx-auto h-12 w-12 mb-4 opacity-50" />
                <p>视频生成完成后将在此显示</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default VideoGeneration;

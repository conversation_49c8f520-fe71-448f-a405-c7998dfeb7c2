import { useState } from "react";
import { invoke } from "@tauri-apps/api/core";
import { Loader2, Image, Upload, Sparkles, Download } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

function TryOnPage() {
  const [modelImageUrl, setModelImageUrl] = useState("");
  const [topGarmentUrl, setTopGarmentUrl] = useState("");
  const [bottomGarmentUrl, setBottomGarmentUrl] = useState("");
  const [tryOnMode, setTryOnMode] = useState("top_only");
  const [isProcessing, setIsProcessing] = useState(false);
  const [resultImageUrl, setResultImageUrl] = useState("");
  const [error, setError] = useState("");
  const [taskId, setTaskId] = useState("");
  const [isPolling, setIsPolling] = useState(false);

  // 阿里云API密钥 - 硬编码配置，不再通过用户输入获取
  // 密钥已从前端界面隐藏，直接在代码中设置
  const aliyunApiKey = "sk-52033e5a00504594befe248ad868a029";

  // 试衣模式选项
  const tryOnModes = [
    { value: "top_only", label: "仅上装（随机生成下装）", requiresTop: true, requiresBottom: false },
    { value: "bottom_only", label: "仅下装（随机生成上装）", requiresTop: false, requiresBottom: true },
    { value: "top_and_bottom", label: "上装+下装", requiresTop: true, requiresBottom: true },
    { value: "dress", label: "连衣裙/连体服", requiresTop: true, requiresBottom: false },
    { value: "keep_bottom", label: "保留原下装", requiresTop: true, requiresBottom: false },
  ];

  // 轮询任务结果
  const pollTaskResult = async (taskId: string) => {
    setIsPolling(true);
    const maxAttempts = 30; // 最多轮询30次
    let attempts = 0;

    const poll = async () => {
      try {
        const result: any = await invoke("query_task_result", {
          taskId: taskId,
          apiKey: aliyunApiKey,
        });

        console.log("任务查询结果:", result);

        if (result.output.task_status === "SUCCEEDED") {
          if (result.output.image_url) {
            setResultImageUrl(result.output.image_url);
            setIsPolling(false);
            setIsProcessing(false);
            return;
          }
        } else if (result.output.task_status === "FAILED") {
          setError(`任务失败: ${result.output.message || result.output.code || "未知错误"}`);
          setIsPolling(false);
          setIsProcessing(false);
          return;
        }

        // 任务还在处理中，继续轮询
        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(poll, 2000); // 2秒后再次查询
        } else {
          setError("任务超时，请稍后重试");
          setIsPolling(false);
          setIsProcessing(false);
        }
      } catch (err) {
        setError(`查询任务结果失败: ${err}`);
        setIsPolling(false);
        setIsProcessing(false);
      }
    };

    poll();
  };

  const handleStartTryOn = async () => {
    const currentMode = tryOnModes.find(mode => mode.value === tryOnMode);

    // 验证必需的输入
    if (!modelImageUrl) {
      setError("请提供模特图片URL");
      return;
    }

    if (currentMode?.requiresTop && !topGarmentUrl) {
      setError("请提供上装图片URL");
      return;
    }

    if (currentMode?.requiresBottom && !bottomGarmentUrl) {
      setError("请提供下装图片URL");
      return;
    }

    setIsProcessing(true);
    setError("");
    setResultImageUrl("");
    setTaskId("");

    try {
      // 如果是保留原下装模式，需要先调用图片分割
      let finalBottomGarmentUrl = bottomGarmentUrl;

      if (tryOnMode === "keep_bottom") {
        try {
          const segmentResult: any = await invoke("segment_garment", {
            apiKey: aliyunApiKey,
            personImageUrl: modelImageUrl,
          });

          if (segmentResult.output.results && segmentResult.output.results.length > 0) {
            finalBottomGarmentUrl = segmentResult.output.results[0].url;
          } else {
            setError("图片分割失败，无法获取下装");
            setIsProcessing(false);
            return;
          }
        } catch (err) {
          setError(`图片分割失败: ${err}`);
          setIsProcessing(false);
          return;
        }
      }

      // 创建试衣任务
      const result: any = await invoke("create_aliyun_tryon_task", {
        apiKey: aliyunApiKey,
        personImageUrl: modelImageUrl,
        tryonType: tryOnMode,
        topGarmentUrl: topGarmentUrl || null,
        bottomGarmentUrl: finalBottomGarmentUrl || null,
      });

      if (result.output.task_id) {
        setTaskId(result.output.task_id);
        // 开始轮询任务结果
        pollTaskResult(result.output.task_id);
      } else {
        setError("创建任务失败");
        setIsProcessing(false);
      }
    } catch (err) {
      setError(`发生错误: ${err}`);
      setIsProcessing(false);
    }
  };

  return (
    <div className="space-y-4 md:space-y-6 w-full">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">AI 虚拟试衣</h1>
        <p className="text-gray-600">提供模特图片和服装图片，查看AI虚拟试衣效果</p>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-700">错误: {error}</p>
        </div>
      )}

      <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 md:gap-6">
        {/* 配置面板 */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                图片配置
              </CardTitle>
              <CardDescription>
                上传或输入模特图片和服装图片的URL
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="tryon-mode">试衣模式</Label>
                <select
                  id="tryon-mode"
                  value={tryOnMode}
                  onChange={(e) => setTryOnMode(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {tryOnModes.map((mode) => (
                    <option key={mode.value} value={mode.value}>
                      {mode.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* 图片配置区域 - 三栏布局 */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* 模特图片 */}
                <div className="space-y-2">
                  <Label htmlFor="model-image-url">模特图片URL</Label>
                  <Input
                    id="model-image-url"
                    type="url"
                    placeholder="https://example.com/model.jpg"
                    value={modelImageUrl}
                    onChange={(e) => setModelImageUrl(e.target.value)}
                  />
                  {modelImageUrl && (
                    <div className="mt-2 bg-gray-50 rounded-md p-2 border">
                      <img
                        src={modelImageUrl}
                        alt="模特图片预览"
                        className="w-full h-40 object-contain rounded-md"
                        onError={(e) => {
                          e.currentTarget.style.display = 'none';
                        }}
                      />
                    </div>
                  )}
                </div>

                {/* 上装输入 */}
                {tryOnModes.find(mode => mode.value === tryOnMode)?.requiresTop && (
                  <div className="space-y-2">
                    <Label htmlFor="top-garment-url">上装图片URL</Label>
                    <Input
                      id="top-garment-url"
                      type="url"
                      placeholder="https://example.com/top.jpg"
                      value={topGarmentUrl}
                      onChange={(e) => setTopGarmentUrl(e.target.value)}
                    />
                    {topGarmentUrl && (
                      <div className="mt-2 bg-gray-50 rounded-md p-2 border">
                        <img
                          src={topGarmentUrl}
                          alt="上装图片预览"
                          className="w-full h-40 object-contain rounded-md"
                          onError={(e) => {
                            e.currentTarget.style.display = 'none';
                          }}
                        />
                      </div>
                    )}
                  </div>
                )}

                {/* 下装输入 */}
                {tryOnModes.find(mode => mode.value === tryOnMode)?.requiresBottom && (
                  <div className="space-y-2">
                    <Label htmlFor="bottom-garment-url">下装图片URL</Label>
                    <Input
                      id="bottom-garment-url"
                      type="url"
                      placeholder="https://example.com/bottom.jpg"
                      value={bottomGarmentUrl}
                      onChange={(e) => setBottomGarmentUrl(e.target.value)}
                    />
                    {bottomGarmentUrl && (
                      <div className="mt-2 bg-gray-50 rounded-md p-2 border">
                        <img
                          src={bottomGarmentUrl}
                          alt="下装图片预览"
                          className="w-full h-40 object-contain rounded-md"
                          onError={(e) => {
                            e.currentTarget.style.display = 'none';
                          }}
                        />
                      </div>
                    )}
                  </div>
                )}
              </div>

              <Button
                className="w-full"
                onClick={handleStartTryOn}
                disabled={
                  isProcessing ||
                  !modelImageUrl ||
                  (tryOnModes.find(mode => mode.value === tryOnMode)?.requiresTop && !topGarmentUrl) ||
                  (tryOnModes.find(mode => mode.value === tryOnMode)?.requiresBottom && !bottomGarmentUrl)
                }
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {isPolling ? "查询结果中..." : "处理中..."}
                  </>
                ) : (
                  <>
                    <Sparkles className="mr-2 h-4 w-4" />
                    开始AI虚拟试衣
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          {/* API 配置状态 */}
          <Card>
            <CardHeader>
              <CardTitle>API 配置状态</CardTitle>
              <CardDescription>
                阿里云API密钥配置状态
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <p className="text-green-700 font-medium">✅ 阿里云API密钥已配置</p>
                <p className="text-xs text-green-600 mt-1">
                  密钥: {aliyunApiKey.substring(0, 8)}...{aliyunApiKey.substring(aliyunApiKey.length - 8)}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* 任务状态 */}
          {(isProcessing || taskId) && (
            <Card>
              <CardHeader>
                <CardTitle>任务状态</CardTitle>
                <CardDescription>
                  当前试衣任务的处理状态
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {taskId && (
                    <p className="text-sm text-gray-600">
                      任务ID: {taskId}
                    </p>
                  )}
                  <div className="flex items-center gap-2">
                    {isProcessing && <Loader2 className="h-4 w-4 animate-spin" />}
                    <span className="text-sm">
                      {isPolling ? "正在查询结果..." : isProcessing ? "正在创建任务..." : "任务完成"}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* 结果面板 */}
        <div>
          <Card className="h-full">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Image className="h-5 w-5" />
                试衣结果
              </CardTitle>
              <CardDescription>
                AI 虚拟试衣结果将显示在这里
              </CardDescription>
            </CardHeader>
            <CardContent>
              {resultImageUrl ? (
                <div className="space-y-4">
                  <div className="bg-gray-50 rounded-lg p-4 border">
                    <img
                      src={resultImageUrl}
                      alt="虚拟试衣结果"
                      className="w-full h-auto max-h-[500px] object-contain rounded-lg mx-auto"
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => {
                        const link = document.createElement('a');
                        link.href = resultImageUrl;
                        link.download = 'tryon-result.jpg';
                        link.click();
                      }}
                    >
                      <Download className="mr-2 h-4 w-4" />
                      下载图片
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-12 text-gray-500">
                  <Sparkles className="h-12 w-12 mb-4 opacity-50" />
                  <p className="text-lg font-medium mb-2">暂无试衣结果</p>
                  <p className="text-sm text-center max-w-sm">配置图片并开始虚拟试衣以查看结果</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

export default TryOnPage;

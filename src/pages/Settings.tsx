import { useState } from "react";
import { Settings } from "lucide-react";

function SettingsPage() {
  // 图像编辑参数配置
  const [editStrength, setEditStrength] = useState<number>(0.7);
  const [guidanceScale, setGuidanceScale] = useState<number>(7.5);
  const [inferenceSteps, setInferenceSteps] = useState<number>(20);
  const [randomSeed, setRandomSeed] = useState<string>("");

  // 硬编码的API密钥信息（仅用于显示配置状态）
  const douBaoApiKey = "2e3e1b1f-b6fb-46c7-a519-343d3146b88f"; // 豆包API密钥
  const aliyunApiKey = "sk-52033e5a00504594befe248ad868a029"; // 阿里云API密钥

  // 硬编码的腾讯云COS配置信息（仅用于显示配置状态）
  const cosRegion = "ap-shanghai";
  const cosBucketName = "tennis-1258507500";

  // 保存设置的函数
  const saveSettings = () => {
    // 这里可以添加保存设置到本地存储或后端的逻辑
    alert("设置已保存！");
  };





  return (
    <div className="container">
      <h1>系统设置</h1>
      <p>配置AI图像处理工具的参数设置</p>

      <div className="config-section">
        <h2><Settings size={20} /> API配置状态</h2>

        {/* API密钥配置状态显示 */}
        <div className="form-group">
          <h3>豆包API配置</h3>
          <div className="upload-success">
            <p>✅ 豆包API密钥已配置 (硬编码)</p>
            <p style={{fontSize: "12px", color: "#666", marginTop: "5px"}}>
              密钥: {douBaoApiKey.substring(0, 8)}...{douBaoApiKey.substring(douBaoApiKey.length - 8)}
            </p>
          </div>
        </div>

        <div className="form-group">
          <h3>阿里云API配置</h3>
          <div className="upload-success">
            <p>✅ 阿里云API密钥已配置 (硬编码)</p>
            <p style={{fontSize: "12px", color: "#666", marginTop: "5px"}}>
              密钥: {aliyunApiKey.substring(0, 8)}...{aliyunApiKey.substring(aliyunApiKey.length - 8)}
            </p>
          </div>
        </div>

        <div className="form-group">
          <h3>腾讯云COS配置</h3>
          <div className="upload-success">
            <p>✅ 腾讯云COS配置已设置 (硬编码)</p>
            <p style={{fontSize: "12px", color: "#666", marginTop: "5px"}}>
              区域: {cosRegion} | 存储桶: {cosBucketName}
            </p>
          </div>
        </div>

        <h3>图像编辑参数设置</h3>
        <div className="form-group">
          <label>编辑强度 (0.0-1.0):</label>
          <input
            type="number"
            min="0.0"
            max="1.0"
            step="0.1"
            value={editStrength}
            onChange={(e) => setEditStrength(parseFloat(e.target.value))}
            className="api-key-input"
            placeholder="0.7"
          />
          <p className="info-text">
            💡 值越高，编辑效果越明显
          </p>
        </div>

        <div className="form-group">
          <label>引导比例 (1.0-20.0):</label>
          <input
            type="number"
            min="1.0"
            max="20.0"
            step="0.5"
            value={guidanceScale}
            onChange={(e) => setGuidanceScale(parseFloat(e.target.value))}
            className="api-key-input"
            placeholder="7.5"
          />
          <p className="info-text">
            💡 控制模型对提示词的遵循程度
          </p>
        </div>

        <div className="form-group">
          <label>推理步数 (10-50):</label>
          <input
            type="number"
            min="10"
            max="50"
            step="1"
            value={inferenceSteps}
            onChange={(e) => setInferenceSteps(parseInt(e.target.value))}
            className="api-key-input"
            placeholder="20"
          />
          <p className="info-text">
            💡 步数越多，质量越高但速度越慢
          </p>
        </div>

        <div className="form-group">
          <label>随机种子 (可选):</label>
          <input
            type="text"
            value={randomSeed}
            onChange={(e) => setRandomSeed(e.target.value)}
            className="api-key-input"
            placeholder="留空则随机生成"
          />
          <p className="info-text">
            💡 相同种子可以生成相似的结果
          </p>
        </div>


        <button
          type="button"
          onClick={saveSettings}
          className="process-btn"
        >
          保存设置
        </button>
      </div>
    </div>
  );
}

export default SettingsPage;

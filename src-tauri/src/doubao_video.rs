use reqwest::Client;
use serde::{Deserialize, Serialize};

// 视频生成请求结构
#[derive(Debug, Serialize)]
pub struct VideoGenerationRequest {
    pub model: String,
    pub content: Vec<ContentItem>,
}

#[derive(Debug, Serialize)]
#[serde(tag = "type")]
pub enum ContentItem {
    #[serde(rename = "text")]
    Text { text: String },
    #[serde(rename = "image_url")]
    ImageUrl { image_url: ImageUrlContent },
}

#[derive(Debug, Serialize)]
pub struct ImageUrlContent {
    pub url: String,
}

// 任务创建响应
#[derive(Debug, Serialize, Deserialize)]
pub struct VideoTaskResponse {
    pub id: String,
}

// 任务查询响应
#[derive(Debug, Serialize, Deserialize)]
pub struct VideoTaskResultResponse {
    pub id: String,
    pub model: String,
    pub status: String,
    pub content: Option<VideoContent>,
    pub seed: Option<i32>,
    pub resolution: Option<String>,
    pub duration: Option<i32>,
    pub ratio: Option<String>,
    pub framespersecond: Option<i32>,
    pub usage: Option<VideoUsage>,
    pub created_at: Option<i64>,
    pub updated_at: Option<i64>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct VideoContent {
    pub video_url: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct VideoUsage {
    pub completion_tokens: Option<i32>,
    pub total_tokens: Option<i32>,
}

// 创建视频生成任务
pub async fn create_video_generation_task(
    api_key: &str,
    prompt: &str,
    image_url: Option<&str>,
) -> Result<VideoTaskResponse, String> {
    let client = Client::new();
    let url = "https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks";

    println!("创建视频生成任务");
    println!("提示词: {}", prompt);
    if let Some(img_url) = image_url {
        println!("参考图片: {}", img_url);
    }

    // 构建内容数组
    let mut content = vec![ContentItem::Text {
        text: prompt.to_string(),
    }];

    // 如果有图片URL，添加到内容中
    if let Some(img_url) = image_url {
        content.push(ContentItem::ImageUrl {
            image_url: ImageUrlContent {
                url: img_url.to_string(),
            },
        });
    }

    let request_body = VideoGenerationRequest {
        model: "doubao-seedance-1-0-pro-250528".to_string(),
        content,
    };

    println!("请求体: {}", serde_json::to_string_pretty(&request_body).unwrap_or_default());

    let response = client
        .post(url)
        .header("Authorization", format!("Bearer {}", api_key))
        .header("Content-Type", "application/json")
        .json(&request_body)
        .send()
        .await
        .map_err(|e| format!("请求失败: {}", e))?;

    let status = response.status();
    let response_text = response.text().await
        .map_err(|e| format!("读取响应失败: {}", e))?;

    println!("创建任务响应状态: {}", status);
    println!("创建任务响应内容: {}", response_text);

    if !status.is_success() {
        return Err(format!("API请求失败 ({}): {}", status, response_text));
    }

    serde_json::from_str(&response_text)
        .map_err(|e| format!("解析响应失败: {} - 响应内容: {}", e, response_text))
}

// 查询视频生成任务结果
pub async fn get_video_generation_result(
    api_key: &str,
    task_id: &str,
) -> Result<VideoTaskResultResponse, String> {
    let client = Client::new();
    let url = format!("https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks/{}", task_id);

    println!("查询视频生成任务结果，任务ID: {}", task_id);

    let response = client
        .get(&url)
        .header("Authorization", format!("Bearer {}", api_key))
        .header("Content-Type", "application/json")
        .send()
        .await
        .map_err(|e| format!("请求失败: {}", e))?;

    let status = response.status();
    let response_text = response.text().await
        .map_err(|e| format!("读取响应失败: {}", e))?;

    println!("查询任务响应状态: {}", status);
    println!("查询任务响应内容: {}", response_text);

    if !status.is_success() {
        return Err(format!("API请求失败 ({}): {}", status, response_text));
    }

    serde_json::from_str(&response_text)
        .map_err(|e| format!("解析响应失败: {} - 响应内容: {}", e, response_text))
}

// Tauri命令：创建视频生成任务
#[tauri::command]
pub async fn create_doubao_video_task(
    api_key: String,
    prompt: String,
    image_url: Option<String>,
) -> Result<VideoTaskResponse, String> {
    create_video_generation_task(&api_key, &prompt, image_url.as_deref()).await
}

// Tauri命令：查询视频生成任务结果
#[tauri::command]
pub async fn get_doubao_video_result(
    api_key: String,
    task_id: String,
) -> Result<VideoTaskResultResponse, String> {
    get_video_generation_result(&api_key, &task_id).await
}

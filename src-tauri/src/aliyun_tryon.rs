use serde::{Serialize, Deserialize};
use reqwest::Client;

#[derive(Serialize)]
struct TryOnInput<'a> {
    person_image_url: &'a str,
    #[serde(skip_serializing_if = "Option::is_none")]
    top_garment_url: Option<&'a str>,
    #[serde(skip_serializing_if = "Option::is_none")]
    bottom_garment_url: Option<&'a str>,
}

#[derive(Serialize)]
struct TryOnParameters {
    resolution: i32,
    restore_face: bool,
}

#[derive(Serialize)]
struct TryOnPayload<'a> {
    model: &'a str,
    input: TryOnInput<'a>,
    parameters: TryOnParameters,
}

// 异步任务创建响应
#[derive(Serialize, Deserialize, Debug)]
pub struct AsyncTaskResponse {
    pub output: AsyncTaskOutput,
    pub request_id: String,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct AsyncTaskOutput {
    pub task_id: String,
    pub task_status: String,
}

// 任务结果查询响应
#[derive(Serialize, Deserialize, Debug)]
pub struct TaskResultResponse {
    pub output: TaskResultOutput,
    pub request_id: String,
    pub usage: Option<TaskUsage>,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct TaskResultOutput {
    pub task_id: String,
    pub task_status: String,
    pub image_url: Option<String>,
    pub submit_time: Option<String>,
    pub scheduled_time: Option<String>,
    pub end_time: Option<String>,
    pub code: Option<String>,
    pub message: Option<String>,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct TaskUsage {
    pub image_count: Option<i32>,
}

// 试衣类型枚举
#[derive(Debug, Clone)]
pub enum TryOnType {
    TopOnly,      // 仅上装，随机生成下装
    BottomOnly,   // 仅下装，随机生成上装
    TopAndBottom, // 上装+下装都指定
    Dress,        // 连衣裙/连体服
    KeepBottom,   // 保留原下装（需要先调用图片分割）
}

// 图片分割输入参数
#[derive(Serialize)]
struct SegmentInput {
    image_url: String,
    category: String, // "bottom" 表示分割下装
}

// 图片分割请求载荷
#[derive(Serialize)]
struct SegmentPayload {
    model: String,
    input: SegmentInput,
}

// 图片分割响应
#[derive(Serialize, Deserialize, Debug)]
pub struct SegmentResponse {
    pub output: SegmentOutput,
    pub request_id: String,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct SegmentOutput {
    pub results: Vec<SegmentResult>,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct SegmentResult {
    pub url: String,
}

// 创建试衣任务（异步）
pub async fn create_tryon_task(
    api_key: &str,
    person_image_url: &str,
    tryon_type: TryOnType,
    top_garment_url: Option<&str>,
    bottom_garment_url: Option<&str>,
) -> Result<AsyncTaskResponse, reqwest::Error> {
    let client = Client::new();
    let url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/image2image/image-synthesis/";

    // 根据试衣类型构建输入参数
    let input = match tryon_type {
        TryOnType::TopOnly | TryOnType::Dress => TryOnInput {
            person_image_url,
            top_garment_url,
            bottom_garment_url: None,
        },
        TryOnType::BottomOnly => TryOnInput {
            person_image_url,
            top_garment_url: None,
            bottom_garment_url,
        },
        TryOnType::TopAndBottom | TryOnType::KeepBottom => TryOnInput {
            person_image_url,
            top_garment_url,
            bottom_garment_url,
        },
    };

    let payload = TryOnPayload {
        model: "aitryon-plus",
        input,
        parameters: TryOnParameters {
            resolution: -1,
            restore_face: true,
        },
    };

    let response = client
        .post(url)
        .header("Authorization", format!("Bearer {}", api_key))
        .header("X-DashScope-Async", "enable")
        .header("Content-Type", "application/json")
        .json(&payload)
        .send()
        .await?;

    response.json::<AsyncTaskResponse>().await
}

// 查询任务结果
pub async fn get_task_result(
    api_key: &str,
    task_id: &str,
) -> Result<TaskResultResponse, String> {
    let client = Client::new();
    let url = format!("https://dashscope.aliyuncs.com/api/v1/tasks/{}", task_id);

    println!("查询任务状态: {}", url);

    let response = client
        .get(&url)
        .header("Authorization", format!("Bearer {}", api_key))
        .send()
        .await
        .map_err(|e| format!("请求失败: {}", e))?;

    let status = response.status();
    let response_text = response.text().await
        .map_err(|e| format!("读取响应失败: {}", e))?;

    println!("响应状态: {}", status);
    println!("响应内容: {}", response_text);

    if !status.is_success() {
        return Err(format!("API请求失败 ({}): {}", status, response_text));
    }

    serde_json::from_str(&response_text)
        .map_err(|e| format!("解析响应失败: {} - 响应内容: {}", e, response_text))
}

// Tauri命令：查询任务结果
#[tauri::command]
pub async fn query_task_result(task_id: String, api_key: String) -> Result<TaskResultResponse, String> {
    get_task_result(&api_key, &task_id).await
}

// 图片分割API - 获取模特下装
pub async fn segment_garment(
    api_key: &str,
    person_image_url: &str,
) -> Result<SegmentResponse, reqwest::Error> {
    let client = Client::new();
    // 使用图片分割专用的API端点
    let url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/image2image/image-segmentation/";

    let payload = SegmentPayload {
        model: "aitryon-seg".to_string(),
        input: SegmentInput {
            image_url: person_image_url.to_string(),
            category: "bottom".to_string(),
        },
    };

    let response = client
        .post(url)
        .header("Authorization", format!("Bearer {}", api_key))
        .header("Content-Type", "application/json")
        .json(&payload)
        .send()
        .await?;

    response.json::<SegmentResponse>().await
}

// 辅助函数：确定试衣类型
pub fn determine_tryon_type(top_garment_url: Option<&str>, bottom_garment_url: Option<&str>) -> Option<TryOnType> {
    match (top_garment_url, bottom_garment_url) {
        (Some(_), Some(_)) => Some(TryOnType::TopAndBottom),
        (Some(_), None) => Some(TryOnType::TopOnly), // 也可能是连衣裙，但API调用相同
        (None, Some(_)) => Some(TryOnType::BottomOnly),
        (None, None) => None, // 无效输入
    }
}
